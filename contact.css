
body {
    position: relative;
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
    overflow: hidden;
    background: linear-gradient(45deg, #12091c, #080324d1, #110236);
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Gradient overlay animation */
body::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite;
    z-index: -1; /* Layer above the blurred image but below the content */
}


/* Modern gradient background */
body::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #12051d, #100522, #170425);
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
    z-index: -2;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Gradient overlay animation */
body::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite;
    z-index: -1; /* Layer above the blurred image but below the content */
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }
}

#join-btn {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
    color: white;
    font-size: 18px;
    font-weight: bold;
    padding: 15px 30px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    outline: none;
    touch-action: manipulation;
    z-index: 1000;
    backdrop-filter: blur(8px);
    animation: pulse 2s infinite;
}

#join-btn i {
    font-size: 20px;
    margin-right: 8px;
    transition: transform 0.3s ease;
}

/* Hover effect */
#join-btn:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
    transform: translate(-50%, -50%) scale(1.05);
}

#join-btn:hover i {
    transform: rotate(15deg);
}

/* Touch and click effect */
#join-btn:active {
    background-color: #e8ece8; /* Even darker background on click */
    box-shadow: 0 3px 4px rgba(0, 0, 0, 0.3); /* Reduced shadow on press */
    transform: translateY(2px); /* Slightly press down */
    border: 2px solid #ffffff; /* Keep light yellow border on press */
}

  
  /* Media query for screens wider than 456px (mobile view) */
  @media (min-width: 456px) {
    #join-btn {
      font-size: 16px; /* Slightly smaller text */
      padding: 10px 20px; /* Adjust padding */
      width: auto; /* Ensure button is adaptive */
      max-width: 300px; /* Limit max width for better visuals */
      border-radius: 12px; /* More rounded corners for mobile */
    }
  }
  
  /* General responsiveness for screens above mobile */
  @media (min-width: 769px) {
    #join-btn {
      font-size: 20px; /* Larger text for bigger screens */
      padding: 14px 28px; /* More padding for emphasis */
    }
  }
  


/* Video Streams Layout */
#video-streams {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
    width: 90%;
    height: 90vh;
    overflow-y: auto;
    max-width: 1920px;
    margin: 0 auto;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.5) transparent;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    touch-action: pan-y pinch-zoom;
}

#video-streams::-webkit-scrollbar {
    width: 6px;
}

#video-streams::-webkit-scrollbar-track {
    background: transparent;
}

#video-streams::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

/* Square-Shaped Video Container */
.video-container {
    position: relative;
    width: 100%;
    padding-top: 100%; /* 1:1 aspect ratio for square shape */
    background-color: #fdfffd;
    border: 2px solid rgb(255, 255, 255);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    will-change: transform;
    backface-visibility: hidden;
}

.video-container:hover {
    transform: scale(1.02);
}

.video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Stream Controls */
#stream-controls {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1.5rem;
    padding: 1.25rem;
    border-radius: 35px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#stream-controls button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(76, 175, 80, 0.3);
    border: 2px solid rgba(76, 175, 80, 0.5);
    cursor: pointer;
    margin: 0;
    transition: all 0.3s ease;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-size: 0;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

#stream-controls button i {
    font-size: 24px;
    transition: all 0.3s ease;
    opacity: 0.9;
}

#stream-controls button:hover i {
    transform: scale(1.2);
}

#stream-controls button.active {
    background: rgba(244, 67, 54, 0.3);
    border-color: rgba(244, 67, 54, 0.5);
}

#stream-controls button.active i {
    color: #f44336;
    opacity: 1;
}
button:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite;
    z-index: -1; /* Layer above the blurred image but below the content */
}
#stream-controls button:hover, #stream-controls button:active {
    background: rgba(76, 175, 80, 0.5);
    border-color: rgba(76, 175, 80, 0.8);
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

#stream-controls button i {
    font-size: 24px;
    pointer-events: none;
}

#stream-controls button span {
    display: none;
}

#stream-controls button:hover i {
    opacity: 1;
}

#stream-controls button:not(.active):hover {
    background: rgba(76, 175, 80, 0.4);
    border-color: rgba(76, 175, 80, 0.8);
}

#stream-controls button.active:hover {
    background: rgba(244, 67, 54, 0.4);
    border-color: rgba(244, 67, 54, 0.8);
}

button:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
    border-color: rgba(255, 255, 255, 1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.6);
}

/* Responsive Layout for Different Screen Sizes */
@media screen and (max-width: 768px) {
    body {
        padding: 8px;
    }

    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 8px;
        height: 85vh;
        padding: 12px;
        touch-action: pan-y pinch-zoom;
    }

    .video-container {
        padding-top: 100%; /* maintain square shape */
        border-width: 1px;
    }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
    #video-streams {
        height: 65vh;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    #stream-controls {
        flex-direction: row;
        padding: 8px;
        gap: 8px;
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: auto;
        justify-content: center;
    }

    button {
        width: auto;
        padding: 8px 15px;
    }

    .video-container {
        padding-top: 56.25%; /* 16:9 aspect ratio */
    }
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        height: 65vh;
        gap: 10px;
        padding: 10px;
    }

    #stream-controls {
        flex-direction: column;
        align-items: center;
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.6);
        padding: 10px;
        backdrop-filter: blur(5px);
    }

    button {
        width: 90%;
        font-size: 14px;
        padding: 12px;
        margin: 5px 0;
        border-radius: 25px;
        background: rgba(255, 255, 255, 0.1);
    }

    #join-btn {
        width: 85%;
        padding: 15px;
        font-size: 16px;
        margin: 10px auto;
        display: block;
    }
}

/* For Tablets and Smaller Laptops */
@media screen and (min-width: 769px) and (max-width: 1024px) {
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    button {
        font-size: 14px;
    }

    #join-btn {
        font-size: 16px;
        padding: 15px 30px;
    }
}

/* For Large Screens (Laptops and Desktops) */
@media screen and (min-width: 1025px) {
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    button {
        font-size: 16px;
    }

    #join-btn {
        font-size: 18px;
        padding: 20px 40px;
    }
}
@media (max-width: 768px) {
    .event {
        flex: 1 1 100%; /* Full width for smaller screens like tablets or mobiles */
    }
}

@media (max-width: 480px) {
    #events {
        padding: 10px;
    }

    .event h3 {
        font-size: 16px;
    }
}

.back-button {
    position: absolute;
    top: 20px;
    left: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    font-size: 20px;
    color: #0d0909;
    background-color: wheat;
    border: none;
    border-radius: 50%;
    text-decoration: none;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.back-button:hover {
    background-color: rgb(255, 255, 255); /* Yellow on hover */
    transform: scale(1.1); /* Slightly enlarge the button on hover */
    box-shadow: 0 0 15px 5px rgb(255, 255, 255); /* Glow effect */
}

.back-button:active {
    background-color: #ffffff; /* Slightly darker yellow when clicked (touch effect) */
    transform: scale(1); /* Normal size when pressed */
    box-shadow: 0 0 10px 4px #ffffff; /* Glow effect when clicked */
}

.back-button i {
    margin: 0;
}
